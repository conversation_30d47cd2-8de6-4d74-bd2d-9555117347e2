import { useState, useEffect, useRef, useCallback } from 'react';
import { useStrategyAssistantStore } from '../stores/strategyAssistantStore';
import { v7 as uuidv7 } from 'uuid';

// Mock data for simulating SSE stream
const mockInterviewPlan = {
    id: "0196b413-fef1-7bed-ab88-1b230712efd8",
    type: "interviewplan",
    content: "店舗のクリスマスセールのために、エネルギッシュな若者層、独身層、ファミリー層を対象にインタビューを実施する",
    targetpersonas: ["エネルギッシュな若者層", "独身層", "ファミリー層"]
};

const mockThoughts = [
    {
        id: "001",
        type: "persona",
        name: "インタビュアー",
        thought: "皆さん、もうすぐクリスマスですね。この季節、お買い物のスタイルや気持ちの変化を教えていただけますか？特に、どんな商品やサービスに注目が集まっていると感じていますか？"
    },
    {
        id: "002",
        type: "persona",
        name: "エネルギッシュな若者層",
        thought: "私にとってクリスマスは“演出”がすべてです。今年は恋人とのディナーに向けて、高級レストランの予約だけでなく、プレゼントや着ていく服にもこだわっています。百貨店のギフトコーナーは既に見に行きました。あと、友人とのホームパーティーでは、SNS映えするデコレーションやフィンガーフード、シャンパンはマスト。最近はInstagramで紹介されていたセレクトショップも回っています。"
    },
    {
        id: "003",
        type: "persona",
        name: "インタビュアー",
        thought: "なるほど、“映える”という点が大事なんですね。では、商品を購入する際に、どういった点でお店を選んでいますか？たとえば価格、立地、商品数、雰囲気など、決め手になるものはありますか？"
    },
    {
        id: "004",
        type: "persona",
        name: "エネルギッシュな若者層",
        thought: "選ぶポイントは3つあって、“センス・スピード・体験”です。まず店のディスプレイが洗練されてると、他も信頼できる。次に時間がないから、在庫確認や予約がオンラインでできるかも大事。最後に、接客がフレンドリーで提案型だとまた行きたくなります。価格は高くても『ここで買いたい』と思わせてくれる店に行きます。"
    },
    {
        id: "005",
        type: "persona",
        name: "独身層",
        thought: "私はちょっと違っていて、自分の“満足感”を重視します。仕事が忙しいので、買い物は効率よく、かつちょっとした贅沢を感じたい。クリスマスは特に、ワインやチーズといった嗜好品、あとは本屋で時間をかけて選ぶ一冊がご褒美です。普段行かないお店や、レビューの高い専門店に足を伸ばすこともあります。"
    },
    {
        id: "006",
        type: "persona",
        name: "インタビュアー",
        thought: "“体験”や“自己満足”というキーワードが出ましたね。では、皆さん、買い物のタイミングはどうされていますか？例えば何週間前から計画するのか、週末にまとめて買うのか、それとも思いついた時に動くのかなど、お聞かせください。"
    },
    {
        id: "007",
        type: "persona",
        name: "ファミリー層",
        thought: "うちは子どもがいるので、12月頭から計画的に動きます。欲しいものリストを書かせて、それを元に店を回ります。価格の比較や在庫確認は私の担当で、特売チラシもチェックしています。食材も含め、日用品から特別なものまでまとめ買いできるスーパーやモールに行くことが多いです。混雑を避けて朝一に行くこともあります。"
    },
    {
        id: "008",
        type: "persona",
        name: "独身層",
        thought: "私は12月中旬頃ですね。セール情報が出揃った頃を狙います。ネットショップのウィッシュリストに入れておいて、価格変動を見ながら決めることが多いです。配送トラブルを避けたいので、早めに動くようにはしています。"
    },
    {
        id: "009",
        type: "persona",
        name: "インタビュアー",
        thought: "ありがとうございます。最後に、店頭とオンラインをどう使い分けているかについて教えてください。どちらにどんな期待をしていますか？"
    },
    {
        id: "010",
        type: "persona",
        name: "エネルギッシュな若者層",
        thought: "プレゼントは“体験”を買いたいので、リアル店舗が多いです。ラッピングの相談や、店員さんとの会話が参考になるので。逆にドリンク類やデコレーションはオンライン。特にSNSで見つけたものはすぐ注文できるのがいいです。"
    },
    {
        id: "011",
        type: "persona",
        name: "ファミリー層",
        thought: "私もオンラインと実店舗をうまく使い分けています。オモチャは子どもと一緒に見て選ぶ楽しさがあるので店頭、でも価格や品ぞろえではネットが優位。家計を守るには両方活用しないと難しいです。"
    },
    {
        id: "012",
        type: "persona",
        name: "独身層",
        thought: "私はほとんどオンラインですね。ただ、プレゼントに本を選ぶ時などは、やっぱり書店に行って装丁や紙の質感を確かめたいです。そういう“感触”はオンラインでは得られません。"
    }
];

const additionalPersonaThoughts = {
    "大学生": [
        {
            type: "persona",
            name: "インタビュアー",
            thought: "大学生の方は、クリスマスシーズンのお買い物についてどのようにお考えですか？"
        },
        {
            type: "persona",
            name: "大学生",
            thought: "クリスマスは友達と集まってパーティーをすることが多いです。みんなで費用をシェアするので、一人あたりの予算は3,000円くらいが目安ですね。お酒や食べ物を持ち寄って、ケーキは事前予約して当日取りに行きます。"
        },
        {
            type: "persona",
            name: "インタビュアー",
            thought: "なるほど、予算を考えながら楽しまれているんですね。お買い物はどこでされることが多いですか？"
        },
        {
            type: "persona",
            name: "大学生",
            thought: "デコレーションや小物は100均で揃えることが多いです。コスパ重視なので、スーパーの特売日を狙ったり、アプリのクーポンを使ったりしています。プレゼント交換をする場合も1,000円程度と決めて、プチギフトを探します。最近はSNSで見つけた安くておしゃれな通販サイトも利用していますね。"
        }
    ],
    "サラリーマン": [
        {
            type: "persona",
            name: "インタビュアー",
            thought: "サラリーマンの方は、クリスマスシーズンのお買い物についてどのようにお考えですか？"
        },
        {
            type: "persona",
            name: "サラリーマン",
            thought: "平日は時間がないから、買い物は通勤途中にさっと済ませたいです。選ぶ時間が惜しいので、“おすすめセット”とか“すぐ買えるギフト”があれば助かりますね。あと、オンラインで見て店舗で受け取れたら完璧です。"
        }
    ],
    "主婦": [
        {
            type: "persona",
            name: "インタビュアー",
            thought: "主婦の方は、クリスマスシーズンのお買い物についてどのようにお考えですか？"
        },
        {
            type: "persona",
            name: "主婦",
            thought: "クリスマスは家族の大切な行事なので、1ヶ月くらい前から少しずつ準備を始めます。子供たちのプレゼントは、欲しいものリストを事前に聞いておいて、セールを狙って購入することが多いですね。"
        },
        {
            type: "persona",
            name: "インタビュアー",
            thought: "計画的にお買い物をされているんですね。クリスマスの食事についてはいかがですか？"
        },
        {
            type: "persona",
            name: "主婦",
            thought: "クリスマスディナーは手作りすることが多いので、スーパーの特売情報をチェックしながら材料を揃えます。チキンやケーキは混雑を避けるために予約しておきます。デコレーションは毎年少しずつ増やしていて、手作りのオーナメントも子供と一緒に作ります。最近はネットスーパーも便利で、重い飲み物などはデリバリーしてもらうこともありますね。家計に影響が出ないよう、ポイントが貯まるお店を選んだり、クレジットカードの還元日に合わせて買い物をしたりと工夫しています。"
        }
    ],
    "シニア層": [
        {
            type: "persona",
            name: "インタビュアー",
            thought: "シニアの方は、クリスマスシーズンのお買い物についてどのようにお考えですか？"
        },
        {
            type: "persona",
            name: "シニア層",
            thought: "やっぱり歳を取ると、人混みは避けたいんです。静かな時間にゆっくり買い物したい。贈り物も“ちゃんと感謝が伝わる”ものを選びたいですね。あと、店員さんの説明が丁寧なところがいいです。"
        }
    ],
    "Z世代": [
        {
            type: "persona",
            name: "インタビュアー",
            thought: "Z世代の方は、クリスマスシーズンのお買い物についてどのようにお考えですか？"
        },
        {
            type: "persona",
            name: "Z世代",
            thought: "クリスマスは友達と過ごすことが多いので、SNSで話題になっているスポットに行ったり、インスタ映えするカフェでパーティーをしたりします。服やコスメなどのプレゼント交換もするので、トレンドアイテムをチェックしています。"
        },
        {
            type: "persona",
            name: "インタビュアー",
            thought: "SNSの影響が大きいんですね。お買い物はどのように行いますか？"
        },
        {
            type: "persona",
            name: "Z世代",
            thought: "ほとんどの買い物はスマホで完結させています。インフルエンサーがおすすめしているブランドや、サステナブルな商品に興味があります。フリマアプリで掘り出し物を探したり、ライブコマースで限定アイテムを購入したりすることも多いです。実店舗に行くときも、事前にSNSでチェックして、クーポンをゲットしてから行きます。友達とシェアして使うサブスクサービスも活用していて、クリスマス用の特別なコーディネートやメイクアイテムを借りることもありますね。環境に配慮したブランドや、社会貢献している企業の商品を選ぶようにしています。"
        }
    ]
};

const mockProspections = [
    {
        id: "2d9e2b8a-4c85-4cd3-88a2-c8c1a9f0ef91",
        type: "expert",
        title: "店舗経営コンサルタント",
        comment: "## 🎄クリスマスセール施策提案（エネルギッシュな若者層・独身層・ファミリー層向け）\n\n### 【1】ギフト導線の強化（全層共通）\n・「価格帯×ターゲット別ギフトMAP」の設置（POP／SNS展開）\n・ラッピング済み即渡し棚を店舗入口に設置\n\n### 【2】空間体験の演出（独身層・パーティー層向け）\n・SNS映えする装飾／キャンドル・香り演出を活用\n・書籍・ギフト雑貨など\"質感を感じる\"商品を特設コーナーに配置\n\n### 【3】オンライン連携施策（全層）\n・ECサイトに「クリスマス特集ページ（パーティー／家族向け別）」開設\n・店舗受取予約（Click & Collect）や在庫確認連携を強化\n\n### 【4】来店動機づくり（ファミリー層向け）\n・サンタと写真が撮れるキッズイベント開催（12月中旬の週末）\n・子ども向け体験型・親子で選べる売場コーナー導入\n\n### 【5】タイミング別販促\n・11月末〜：早割ギフト＆福袋先行予約\n・12月中旬：まとめ買いセール（ホームパーティー需要対応）\n・12/22〜24：当日用ギフト・冷蔵食品のセット販売を強化"
    }
];

const additionalPersonaProspections = [
    {
        type: "expert",
        title: "経営コンサルタント",
        comment: "## 🎄クリスマスセール施策提案（多層型戦略：全5顧客層対応）\n\n### 【1】導線＆ギフト強化（全層共通）\n・「価格帯×ターゲット別ギフトMAP」の設置（POP／SNS展開）\n・ラッピング済み即渡し棚を店舗入口に設置\n・「迷ったらコレ」ギフト推薦タグの設置（サラリーマン・シニア層向け）\n\n### 【2】空間演出（パーティー層・独身層向け）\n・SNS映え装飾／アロマ演出（時間帯別で調整）\n・\"感触や紙質\"が選べる書籍・雑貨コーナー強化\n\n### 【3】静かな買い物支援（シニア層向け）\n・開店後1時間を「ゆったりタイム」として告知（BGM音量控えめ）\n・丁寧な接客対応強化＆「贈り物相談カウンター」設置\n\n### 【4】時短＆導線設計（サラリーマン層向け）\n・駅近店では「15分で完結！即決ギフト棚」を導入\n・オンラインから店舗受取（Click & Collect）対象商品に「当日渡し保証」マーク追加\n\n### 【5】ファミリー層向け体験\n・子ども向け写真イベント（サンタとの記念撮影）\n・親子で選べる体験型ギフト展示\n\n### 【6】タイミング別プロモーション\n・11月下旬〜：シニア＆ファミリー向け早割プロモーション\n・12月中旬：まとめ買い・ホームパーティー用パッケージ強化\n・12/22〜24：即日受取対応商品集中展開（サラリーマン＆独身層向け）\n\n### まとめ\nシニア層とサラリーマン層のニーズは、\"静けさ・丁寧さ\"と\"効率・時短\"という対照的な特性を持つ。各層の購買体験価値を最大化するには、時間帯・導線・コミュニケーション手段の最適化が重要である。特に時間帯別顧客層行動パターンに応じた売場演出とスタッフ配置は、クリスマス期のCS向上と回転率アップの鍵となる。"
    }
];

const generateNewConversationFromEdit = (editedMessage) => {

    if (editedMessage.includes("予算") || editedMessage.includes("価格") || editedMessage.includes("お金")) {
        return [
            {
                type: "persona",
                name: "ファミリー層",
                thought: "子どもへのプレゼントが中心なので、1人あたり3000〜5000円。3人分とかになると、トータルで1万円は超えますね。なるべくお得に買えるところがあると助かります。"
            },
            {
                type: "persona",
                name: "独身層",
                thought: "自分へのご褒美として少し高めのものを選ぶことが多いです。だいたい1〜2万円くらい。品質とかデザインを重視して選んでます。"
            },
            {
                type: "persona",
                name: "エネルギッシュな若者層",
                thought: "とにかく盛り上がる小物やお菓子、ドリンクとかを人数分買うので、全体で5000円〜1万円くらいです。映えればOK。単価より雰囲気。"
            },
            {
                type: "persona",
                name: "シニア層",
                thought: "孫や親戚に贈るためのプレゼントが中心で、1人に2000〜3000円くらいかな。複数人に贈るから、合計1万円くらいまで。気持ちが伝われば十分。"
            },
            {
                type: "persona",
                name: "サラリーマン",
                thought: "職場の人や友達用に、2000円以下でサッと買えるものを探します。安くても“ちゃんとして見える”のがいいですね。"
            },
        ];
    } else if (editedMessage.includes("時間") || editedMessage.includes("いつ") || editedMessage.includes("タイミング")) {
        return [
            {
                type: "persona",
                name: "ファミリー層",
                thought: "子供たちの学校が終わってから、平日の夕方に買い物に行くことが多いです。週末は混雑するので、できるだけ避けたいですね。"
            },
            {
                type: "persona",
                name: "インタビュアー",
                thought: "時間帯を工夫されているんですね。エネルギッシュな若者層の方はいかがですか？"
            },
            {
                type: "persona",
                name: "エネルギッシュな若者層",
                thought: "私は仕事帰りの夜遅い時間や、休日の朝一番に買い物をすることが多いです。人が少ない時間帯の方が、ゆっくり選べるので好きです。"
            }
        ];
    } else if (editedMessage.includes("場所") || editedMessage.includes("どこ") || editedMessage.includes("店舗")) {
        return [
            {
                type: "persona",
                name: "独身層",
                thought: "オンラインショッピングを主に利用しています。実店舗に行く場合は、アクセスの良い駅近くのデパートやショッピングモールを選びます。"
            },
            {
                type: "persona",
                name: "インタビュアー",
                thought: "便利な場所を選ばれているんですね。ファミリー層の方はどのような場所で買い物をされますか？"
            },
            {
                type: "persona",
                name: "ファミリー層",
                thought: "車でアクセスしやすく、駐車場が広いショッピングモールをよく利用します。子供連れでも安心して買い物ができる環境が重要ですね。"
            }
        ];
    } else {
        return [
            {
                type: "persona",
                name: "エネルギッシュな若者層",
                thought: "そうですね、その点についてはとても興味深いです。私たちの世代では、SNSでの情報収集が重要な要素になっています。"
            },
            {
                type: "persona",
                name: "インタビュアー",
                thought: "SNSの影響は大きいのですね。他の皆さんはいかがでしょうか？"
            },
            {
                type: "persona",
                name: "独身層",
                thought: "私も情報収集は大切にしていますが、実際の商品レビューや口コミを重視しています。失敗したくないので、事前調査は欠かせません。"
            },
            {
                type: "persona",
                name: "ファミリー層",
                thought: "家族のことを考えると、安全性や品質が最優先です。価格も重要ですが、子供たちが喜ぶかどうかが一番の判断基準になります。"
            }
        ];
    }
};

const generateNewProspectionsFromEdit = (editedMessage) => {
    if (editedMessage.includes("予算") || editedMessage.includes("価格") || editedMessage.includes("お金")) {
        return [
            {
                type: "expert",
                title: "経営コンサルタント",
                comment: "## 🎄予算特性を踏まえたクリスマスセール施策（価格帯別＋目的別）\n\n### 【1】価格帯別ギフト導線の強化\n・「〜2000円」「〜5000円」「1万円以上」など予算別棚を設置（色で視認性向上）\n・各棚に「ターゲット別おすすめシール」付与（例：「職場向け」「孫にぴったり」「ご褒美」など）\n\n### 【2】ファミリー層：まとめ買い＆早期特典\n・「子ども3人分セット割」などボリュームディスカウント\n・早期購入特典（11月中）でポイント2倍などのインセンティブ\n\n### 【3】独身層：高品質・限定モデル訴求\n・1〜2万円台の高単価ギフトを「ご褒美コレクション」としてブランド化\n・体験型ギフト（例：カフェチケットや本革小物）とのセット販売\n\n### 【4】エネルギッシュな若者層：セット商品×映え訴求\n・「映え小物セット」「プチギフトまとめ買い10％OFF」\n・Instagram投稿キャンペーンを活用したリアル販促連動\n\n### 【5】シニア層：手頃＋気持ち伝わる贈答支援\n・2000〜3000円中心のギフトコーナー強化（和雑貨／食品／手紙付きギフト）\n・「孫に贈る安心ギフト」POPで選びやすさを演出\n\n### 【6】サラリーマン層：低単価×高見え対策\n・「2000円以下で\"きちんと見える\"ギフト」棚設置（文具・お菓子・ボディケア品）\n・\"時短で選べる\"アイコン＋在庫即時反映POP（平日夕方に強化）\n\n### 【7】デジタル連動による在庫・価格通知\n・事前にLINEなどで「予算×ジャンル別おすすめ」通知\n・Web在庫連動で「今すぐ受取可」のステータス表示\n\n### まとめ\n各層の平均予算レンジと購買意図を精査し、価格帯別・目的別にパッケージ設計を見直すことで、顧客の選択負荷を軽減し、購入単価・点数の両方を押し上げる設計が実現可能となる。"
            }
        ];
    } else if (editedMessage.includes("時間") || editedMessage.includes("いつ") || editedMessage.includes("タイミング")) {
        return [
            {
                type: "expert",
                title: "経営コンサルタント",
                comment: "顧客の購買タイミングの分析から、営業時間の最適化と人員配置の見直しが必要です。ピーク時間とオフピーク時間を活用した効率的な運営戦略を提案します。"
            },
            {
                type: "expert",
                title: "マーケティングスペシャリスト",
                comment: "時間帯別の顧客行動パターンを活用して、タイムセール戦略やターゲット別の販促タイミングの最適化を図ることができます。特に平日夕方と休日朝の時間帯に注目すべきです。"
            },
            {
                type: "expert",
                title: "データアナリスト",
                comment: "購買時間帯のデータから、顧客の生活パターンと購買行動の相関関係が明らかになりました。この情報を基に、時間帯別の在庫管理と販売予測の精度向上が可能です。"
            }
        ];
    } else {
        return [
            {
                type: "expert",
                title: "経営コンサルタント",
                comment: "更新された顧客インサイトから、新たなビジネス機会が見えてきました。顧客の変化するニーズに対応するため、サービス提供方法の革新が必要です。"
            },
            {
                type: "expert",
                title: "マーケティングスペシャリスト",
                comment: "編集された質問から得られた新しい顧客の声は、マーケティング戦略の見直しの重要性を示しています。より深い顧客理解に基づいた、パーソナライズされたアプローチが効果的です。"
            },
            {
                type: "expert",
                title: "データアナリスト",
                comment: "新しい質問パターンから収集されたデータは、従来の分析モデルの改善点を示唆しています。より精密な顧客セグメンテーションと予測モデルの構築が可能になります。"
            }
        ];
    }
};

export const usePersonaStream = () => {
    const { activeQuestionId } = useStrategyAssistantStore();
    const [interviewPlan, setInterviewPlan] = useState(null);
    const [thoughts, setThoughts] = useState([]);
    const [prospections, setProspections] = useState([]);
    const [status, setStatus] = useState('idle');
    const [interviewPlanLoading, setInterviewPlanLoading] = useState(false);
    const [thoughtsLoading, setThoughtsLoading] = useState(false);
    const [prospectionsLoading, setProspectionsLoading] = useState(false);

    const timersRef = useRef([]);
    const hasInitializedRef = useRef(false);

    const resetStream = useCallback(() => {
        setInterviewPlan(null);
        setThoughts([]);
        setProspections([]);
        setStatus('idle');
        setInterviewPlanLoading(false);
        setThoughtsLoading(false);
        setProspectionsLoading(false);

        timersRef.current.forEach(timer => clearTimeout(timer));
        timersRef.current = [];
        hasInitializedRef.current = false;
    }, []);

    const startStreamFromMessage = useCallback((newQuestionId, editedMessageId, newMessageText, storedPersonas = []) => {
        const id = newQuestionId || activeQuestionId;

        if (!id) {
            console.error('No question ID provided to usePersonaStream');
            setStatus('error');
            return;
        }

        console.log(`usePersonaStreamMock: startStreamFromMessage called with id=${id}, editedMessageId=${editedMessageId}, newMessageText=${newMessageText}`);
        console.log('Current thoughts:', thoughts);
        console.log('Stored personas:', storedPersonas);

        timersRef.current.forEach(timer => clearTimeout(timer));
        timersRef.current = [];

        const sourceMessages = thoughts.length > 0 ? thoughts : storedPersonas;
        console.log('Using source messages:', sourceMessages);

        const editedMessageIndex = sourceMessages.findIndex(msg => msg.id === editedMessageId);
        if (editedMessageIndex === -1) {
            console.error('Edited message not found in source messages');
            console.log('Looking for ID:', editedMessageId);
            console.log('Available IDs:', sourceMessages.map(msg => ({ id: msg.id, name: msg.name })));
            return;
        }

        console.log(`Found edited message at index ${editedMessageIndex}`);

        const updatedThoughts = sourceMessages.slice(0, editedMessageIndex + 1).map(msg => ({
            id: msg.id,
            type: "persona",
            name: msg.name,
            thought: msg.id === editedMessageId ? newMessageText : msg.thought
        }));

        setThoughts(updatedThoughts);
        setProspections([]);
        setStatus('loading');
        setThoughtsLoading(true);
        setProspectionsLoading(false);

        const newConversation = generateNewConversationFromEdit(newMessageText);

        let currentTime = 1000;

        newConversation.forEach((thought, index) => {
            const timer = setTimeout(() => {
                console.log(`Sending new persona message ${index + 1}`, thought);
                setThoughts(prev => [...prev, { ...thought, id: uuidv7() }]);
                if (index === newConversation.length - 1) {
                    const thankYouTimer = setTimeout(() => {
                        const thankYouMessage = {
                            id: uuidv7(),
                            type: "persona",
                            name: "インタビュアー",
                            thought: "皆さん、貴重なお時間をいただき、ありがとうございました。今日のお話は大変参考になりました。いただいたご意見を参考に、より良いサービスを提供できるよう努めてまいります。"
                        };
                        console.log('Sending interviewer thank you message', thankYouMessage);
                        setThoughts(prev => [...prev, thankYouMessage]);
                        setThoughtsLoading(false);
                        setProspectionsLoading(true);
                    }, 600);
                    timersRef.current.push(thankYouTimer);
                }
            }, currentTime);
            timersRef.current.push(timer);
            currentTime += 600;
        });

        const expertStartTime = currentTime + 1000;
        const newProspections = generateNewProspectionsFromEdit(newMessageText);
        newProspections.forEach((prospection, index) => {
            const timer = setTimeout(() => {
                const prospectionWithId = {
                    ...prospection,
                    id: uuidv7()
                };
                console.log(`Sending new expert message ${index + 1}`, prospectionWithId);
                setProspections(prev => [...prev, prospectionWithId]);
                if (index === newProspections.length - 1) {
                    setProspectionsLoading(false);
                }
            }, expertStartTime + (index * 800));
            timersRef.current.push(timer);
        });

        const finalCompleteTime = expertStartTime + (newProspections.length * 800) + 1000;
        const completeTimer = setTimeout(() => {
            console.log('usePersonaStreamMock: New stream completed, setting status=completed');
            setStatus('completed');
        }, finalCompleteTime);
        timersRef.current.push(completeTimer);

    }, [activeQuestionId, thoughts]);

    const startStream = useCallback((newQuestionId, additionalPersonas = []) => {
        const id = newQuestionId || activeQuestionId;

        if (!id) {
            console.error('No question ID provided to usePersonaStream');
            setStatus('error');
            return;
        }

        const isAddingPersonas = additionalPersonas.length > 0;
        console.log(`usePersonaStreamMock: startStream called with id=${id}, additionalPersonas=`, additionalPersonas);
        console.log(`usePersonaStreamMock: isAddingPersonas=${isAddingPersonas}`);

        if (!isAddingPersonas) {
            console.log('usePersonaStreamMock: Resetting stream for new question');
            resetStream();
            hasInitializedRef.current = true;
        } else {
            console.log('usePersonaStreamMock: Preparing for additional personas');
            timersRef.current.forEach(timer => clearTimeout(timer));
            timersRef.current = [];

            setStatus('loading');
            setThoughtsLoading(true);
            setProspectionsLoading(false);
        }

        setStatus('loading');
        console.log(`Starting mock stream for question ${id}${isAddingPersonas ? ' with additional personas' : ''}`);

        let currentTime = 300;

        if (!isAddingPersonas) {
            setInterviewPlanLoading(true);
            const interviewPlanTimer = setTimeout(() => {
                console.log('Sending interview plan', mockInterviewPlan);
                setInterviewPlan(mockInterviewPlan);
                setInterviewPlanLoading(false);
                setThoughtsLoading(true);
            }, currentTime);
            timersRef.current.push(interviewPlanTimer);
            currentTime += 800;

            mockThoughts.forEach((thought, index) => {
                const timer = setTimeout(() => {
                    console.log(`Sending persona message ${index + 1}`, thought);
                    setThoughts(prev => [...prev, thought]);
                    if (index === mockThoughts.length - 1 && additionalPersonas.length === 0) {
                        const thankYouTimer = setTimeout(() => {
                            const thankYouMessage = {
                                id: uuidv7(),
                                type: "persona",
                                name: "インタビュアー",
                                thought: "皆さん、貴重なお時間をいただき、ありがとうございました。今日のお話は大変参考になりました。いただいたご意見を参考に、より良いサービスを提供できるよう努めてまいります。"
                            };
                            console.log('Sending interviewer thank you message', thankYouMessage);
                            setThoughts(prev => [...prev, thankYouMessage]);
                            setThoughtsLoading(false);
                            setProspectionsLoading(true);
                        }, 600);
                        timersRef.current.push(thankYouTimer);
                    }
                }, currentTime);
                timersRef.current.push(timer);
                currentTime += 600;
            });
        }

        if (additionalPersonas.length > 0) {
            console.log('usePersonaStreamMock: Processing additional personas:', additionalPersonas);
            let additionalThoughts = [];
            additionalPersonas.forEach(personaName => {
                console.log(`usePersonaStreamMock: Looking for thoughts for persona: ${personaName}`);
                if (additionalPersonaThoughts[personaName]) {
                    console.log(`usePersonaStreamMock: Found thoughts for ${personaName}:`, additionalPersonaThoughts[personaName]);
                    additionalThoughts = [
                        ...additionalThoughts,
                        ...additionalPersonaThoughts[personaName].map(thought => ({
                            ...thought,
                            id: uuidv7()
                        }))
                    ];
                } else {
                    console.warn(`usePersonaStreamMock: No thoughts found for persona: ${personaName}`);
                }
            });

            console.log('usePersonaStreamMock: Total additional thoughts to add:', additionalThoughts.length);

            if (additionalThoughts.length > 0) {
                additionalThoughts.forEach((thought, index) => {
                    const timer = setTimeout(() => {
                        console.log(`Sending additional persona message ${index + 1}`, thought);
                        setThoughts(prev => [...prev, thought]);
                        if (index === additionalThoughts.length - 1) {
                            console.log('usePersonaStreamMock: All additional thoughts added, adding thank you message');
                            // Add interviewer thank you message after additional personas
                            const thankYouTimer = setTimeout(() => {
                                const thankYouMessage = {
                                    id: uuidv7(),
                                    type: "persona",
                                    name: "インタビュアー",
                                    thought: "追加でご参加いただいた皆さんもありがとうございました。多様なご意見をお聞かせいただき、より深い洞察を得ることができました。"
                                };
                                console.log('Sending additional interviewer thank you message', thankYouMessage);
                                setThoughts(prev => [...prev, thankYouMessage]);
                                setThoughtsLoading(false);
                                setProspectionsLoading(true);
                            }, 600);
                            timersRef.current.push(thankYouTimer);
                        }
                    }, currentTime);
                    timersRef.current.push(timer);
                    currentTime += 600;
                });
            } else {
                console.warn('usePersonaStreamMock: No additional thoughts to add, skipping this step');
                setThoughtsLoading(false);
                setProspectionsLoading(true);

                const timer = setTimeout(() => {
                    console.log('usePersonaStreamMock: Starting expert prospections after delay');
                }, 1000);
                timersRef.current.push(timer);
                currentTime += 1000;
            }
        }

        console.log('usePersonaStreamMock: Setting up expert prospections');

        const expertStartTime = currentTime + 1000;

        if (isAddingPersonas && additionalPersonas.length > 0) {
            console.log('usePersonaStreamMock: Using updated expert prospections for added personas:', additionalPersonas);

            setProspections([]);
            console.log('usePersonaStreamMock: Using the standard additional persona prospections');

            additionalPersonaProspections.forEach((prospection, index) => {
                const timer = setTimeout(() => {
                    const prospectionWithId = {
                        ...prospection,
                        id: uuidv7()
                    };
                    console.log(`Sending updated expert message ${index + 1}`, prospectionWithId);
                    setProspections(prev => [...prev, prospectionWithId]);
                    if (index === additionalPersonaProspections.length - 1) {
                        console.log('usePersonaStreamMock: All updated expert prospections added, setting prospectionsLoading=false');
                        setProspectionsLoading(false);
                    }
                }, expertStartTime + (index * 800));
                timersRef.current.push(timer);
            });
        } else {
            console.log('usePersonaStreamMock: Using default expert prospections');
            mockProspections.forEach((prospection, index) => {
                const timer = setTimeout(() => {
                    console.log(`Sending expert message ${index + 1}`, prospection);
                    setProspections(prev => [...prev, prospection]);
                    if (index === mockProspections.length - 1) {
                        console.log('usePersonaStreamMock: All expert prospections added, setting prospectionsLoading=false');
                        setProspectionsLoading(false);
                    }
                }, expertStartTime + (index * 800));
                timersRef.current.push(timer);
            });
        }

        const prospectionCount = isAddingPersonas && additionalPersonas.length > 0
            ? additionalPersonaProspections.length
            : mockProspections.length;
        const finalCompleteTime = expertStartTime + (prospectionCount * 800) + 1000;

        const completeTimer = setTimeout(() => {
            console.log('usePersonaStreamMock: Stream completed, setting status=completed');
            setStatus('completed');
        }, finalCompleteTime);
        timersRef.current.push(completeTimer);

        console.log(`usePersonaStreamMock: Stream will complete in ${finalCompleteTime}ms`);

    }, [activeQuestionId, resetStream]);

    useEffect(() => {
        return () => {
            timersRef.current.forEach(timer => clearTimeout(timer));
        };
    }, []);

    useEffect(() => {
        if (activeQuestionId) {
            if (!hasInitializedRef.current) {
                console.log('usePersonaStreamMock: Initializing stream for new question ID:', activeQuestionId);
                hasInitializedRef.current = true;
                startStream(activeQuestionId);
            }
        } else {
            hasInitializedRef.current = false;
        }
    }, [activeQuestionId, startStream]);

    return {
        thoughts,
        prospections,
        interviewPlan,
        status,
        isLoading: status === 'loading',
        isComplete: status === 'completed',
        isError: status === 'error',
        interviewPlanLoading,
        thoughtsLoading,
        prospectionsLoading,
        startStream,
        startStreamFromMessage,
        resetStream
    };
};