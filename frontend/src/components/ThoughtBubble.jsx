import { useState, useRef, useEffect } from 'react';
import { getPersonaAvatar } from '../utils/iconMappings';
import { Edit, Check, X } from 'lucide-react';

const ThoughtBubble = ({ text, color, personaName, customClassName = '', isComplete = false, onEdit = null, thoughtId = null }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editText, setEditText] = useState(text);
    const textareaRef = useRef(null);
    const colorClasses = {
        blue: { bg: 'bg-[#f4f4f4]', border: 'border-[#f4f4f4]' },
        cyan: { bg: 'bg-[#A6C8FF]', border: 'border-[#A6C8FF]' },
        purple: { bg: 'bg-[#ffffff]', border: 'border-[#8A3FFC]' },
        green: { bg: 'bg-[#EDF5FF]', border: 'border-[#EDF5FF]' },
        pink: { bg: 'bg-[#F6F2FF]', border: 'border-[#F6F2FF]' },
        orange: { bg: 'bg-[#ffffff]', border: 'border-[#0072C3]' },
        lightblue: { bg: 'bg-[#E5F6FF]', border: 'border-[#E5F6FF]' },
        lightpurple: { bg: 'bg-[#ffffff]', border: 'border-[#1192E8]' },
        lightcyan: { bg: 'bg-[#E8DAFF]', border: 'border-[#E8DAFF]' },
        lightpink: { bg: 'bg-[#BAE6FF]', border: 'border-[#BAE6FF]' },
        lightorange: { bg: 'bg-[#ffffff]', border: 'border-[#78A9FF]' },
        lightgreen: { bg: 'bg-[#D0E2FF]', border: 'border-[#D0E2FF]' },
    };

    const classes = colorClasses[color] || colorClasses.orange;

    const isInterviewer = personaName === "インタビュアー";
    const canEdit = isInterviewer && isComplete && onEdit && thoughtId;

    const avatarSrc = isInterviewer
        ? "/avatar1.png"
        : getPersonaAvatar(personaName);

    const bubbleWidthClass = isEditing
        ? 'w-full md:max-w-[80%] lg:max-w-[70%] xl:max-w-[60%]'
        : 'w-fit max-w-full md:max-w-[80%] lg:max-w-[70%] xl:max-w-[60%]';

    // Auto-resize textarea function
    const autoResizeTextarea = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
        }
    };

    // Effect to auto-resize when editText changes
    useEffect(() => {
        if (isEditing) {
            autoResizeTextarea();
        }
    }, [editText, isEditing]);

    // Effect to auto-resize when entering edit mode
    useEffect(() => {
        if (isEditing && textareaRef.current) {
            setTimeout(() => {
                autoResizeTextarea();
                textareaRef.current.focus();
            }, 0);
        }
    }, [isEditing]);

    const handleEditClick = () => {
        setIsEditing(true);
        setEditText(text);
    };

    const handleSave = () => {
        if (onEdit && thoughtId && editText.trim() !== text) {
            onEdit(thoughtId, editText.trim());
        }
        setIsEditing(false);
    };

    const handleCancel = () => {
        setEditText(text);
        setIsEditing(false);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
            handleSave();
        } else if (e.key === 'Escape') {
            handleCancel();
        }
    };

    return (
        <div className={`flex ${isInterviewer ? 'flex-row-reverse' : 'flex-row'} gap-2 mb-3 ${customClassName}`}>
            {/* アバター */}
            <img src={avatarSrc} alt={`${personaName} Avatar`} className="w-8 h-8 rounded-full flex-shrink-0" />

            <div className={`flex flex-col ${isInterviewer ? 'items-end' : 'items-start'} ${isEditing ? 'w-full max-w-[90%]' : 'max-w-[90%] w-auto'}`}>
                {/* PersonaName */}
                <span className="text-lg font-bold mb-0.5">{personaName}</span>

                {/* Bubble */}
                <div className={`
                    ${bubbleWidthClass}
                    ${classes.bg}
                    relative
                    p-3 md:p-4
                    rounded-lg
                    border-4
                    ${classes.border}
                    text-sm md:text-base
                    min-h-[3rem]
                    shadow-sm
                    block
                    group
                `}>
                    {isEditing ? (
                        <div className="w-full min-w-full">
                            <textarea
                                ref={textareaRef}
                                value={editText}
                                onChange={(e) => setEditText(e.target.value)}
                                onKeyDown={handleKeyDown}
                                className="w-full min-w-full bg-transparent border-none resize-none focus:outline-none text-sm md:text-base block overflow-hidden"
                                style={{
                                    minHeight: '3rem',
                                    width: '100%',
                                    minWidth: '100%'
                                }}
                                rows={1}
                            />
                            <div className="flex gap-2 mt-2 justify-end">
                                <button
                                    onClick={handleSave}
                                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                                    title="保存 (Ctrl+Enter)"
                                >
                                    <Check size={16} className="text-green-600" />
                                </button>
                                <button
                                    onClick={handleCancel}
                                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                                    title="キャンセル (Esc)"
                                >
                                    <X size={16} className="text-red-600" />
                                </button>
                            </div>
                        </div>
                    ) : (
                        <>
                            <span className="whitespace-pre-wrap">
                                {text}
                            </span>
                            {canEdit && (
                                <button
                                    onClick={handleEditClick}
                                    className="absolute bottom-2 right-2 p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-200 transition-all duration-200"
                                    title="編集"
                                >
                                    <Edit size={14} className="text-gray-600" />
                                </button>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ThoughtBubble;
